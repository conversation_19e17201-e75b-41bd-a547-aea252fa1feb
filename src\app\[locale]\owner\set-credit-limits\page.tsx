
"use client";

import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Landmark } from 'lucide-react';
import { useScopedI18n } from '@/lib/i18n/client';

export default function OwnerSetCreditLimitsPage() {
  const t = useScopedI18n('common'); // For nav title
  const tSpecific = useScopedI18n('ownerSetCreditLimits'); // Specific scope for page content

  return (
    <AuthenticatedLayout expectedRole="owner">
      <div className="space-y-6 p-1">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <Landmark className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold text-foreground">{t('nav_setCreditLimitsOwner')}</h1>
            <p className="text-muted-foreground">
              {tSpecific('description')}
            </p>
          </div>
        </div>

        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle>{tSpecific('underDevelopmentTitle')}</CardTitle>
            <CardDescription>
              {tSpecific('underDevelopmentMessage')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground text-center py-6">
              {tSpecific('featureComingSoon')}
            </p>
          </CardContent>
        </Card>
      </div>
    </AuthenticatedLayout>
  );
}
